# LineLayer.ts 架构图和详细导读

## 概述

`static/js/Workout/LineLayer.ts` 是 Suunto 地图应用中的核心 WebGL 渲染模块，负责在 3D 地图上高性能地渲染运动轨迹线条和点。该模块实现了自定义的 WebGL 着色器、智能的数据更新策略和优化的渲染管线，是整个应用中最复杂和最关键的可视化组件。

## 整体架构图

```mermaid
graph TB
    subgraph "LineLayer 主类"
        A[LineLayer<br/>CustomLayerInterface]
    end
    
    subgraph "渲染器层"
        B[LineRenderer<br/>线条渲染器]
        C[PointRenderer<br/>点渲染器]
        D[Renderer<br/>基础渲染器]
    end
    
    subgraph "WebGL 着色器"
        E[顶点着色器<br/>Vertex Shader]
        F[片段着色器<br/>Fragment Shader]
    end
    
    subgraph "数据处理"
        G[数据缓冲区<br/>Float32Array]
        H[批处理系统<br/>Batch Processing]
        I[距离优先更新<br/>Distance-based Update]
    end
    
    subgraph "数学工具"
        J[矩阵运算<br/>Matrix Operations]
        K[坐标转换<br/>Coordinate Transform]
    end
    
    A --> B
    A --> C
    B --> D
    C --> D
    D --> E
    D --> F
    A --> G
    A --> H
    A --> I
    A --> J
    A --> K
```

## 类层次结构图

```mermaid
classDiagram
    class Renderer {
        +WebGLProgram program
        +WebGLBuffer buffer
        +WebGLBuffer distBuffer
        +WebGLUniformLocation uMatrix
        +WebGLUniformLocation uCamera
        +WebGLUniformLocation uThickness
        +number count
        +constructor(gl, vert, frag)
        +update(gl, data, count)
        +updateDist(gl, data)
    }
    
    class LineRenderer {
        +constructor(gl)
        +render(gl, matrix, thickness, position, camera)
    }
    
    class PointRenderer {
        +constructor(gl)
        +render(gl, matrix, thickness, position, camera)
    }
    
    class LineLayer {
        +string id
        +number[][] pts
        +Float32Array lineData
        +Float32Array pointData
        +LineRenderer lineRenderer
        +PointRenderer pointRenderer
        +onAdd(map, gl)
        +updateData(updateAll)
        +render(gl, matrix)
        +setPosition(position)
    }
    
    Renderer <|-- LineRenderer
    Renderer <|-- PointRenderer
    LineLayer --> LineRenderer
    LineLayer --> PointRenderer
```

---

## 核心组件详细分析

### 1. 数学工具函数

#### 矩阵运算系统
```typescript
function buildMatrix(a: number[], b: number[], c: number[], d: number[]) {
  return a.concat(b, c, d);
}

function multiplyMat4Vec4(m: number[], [x, y, z, w]: number[]) {
  return [
    x * m[0] + y * m[4] + z * m[8] + w * m[12],
    x * m[1] + y * m[5] + z * m[9] + w * m[13],
    x * m[2] + y * m[6] + z * m[10] + w * m[14],
    x * m[3] + y * m[7] + z * m[11] + w * m[15],
  ];
}
```

**功能特点：**
- **高性能矩阵运算**：手工优化的 4x4 矩阵乘法
- **3D 变换支持**：支持平移、旋转、缩放等变换
- **内存效率**：避免创建临时对象，直接操作数组

### 2. Renderer 基础类

#### WebGL 程序管理
```typescript
class Renderer {
  constructor(gl: WebGLRenderingContext, vert: string, frag: string) {
    // 创建和编译着色器
    const vertexShader = gl.createShader(gl.VERTEX_SHADER);
    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
    
    // 编译验证
    gl.shaderSource(vertexShader, vert);
    gl.compileShader(vertexShader);
    
    // 创建程序并链接
    this.program = gl.createProgram();
    gl.attachShader(this.program, vertexShader);
    gl.attachShader(this.program, fragmentShader);
    gl.linkProgram(this.program);
    
    // 获取 uniform 和 attribute 位置
    this.uMatrix = gl.getUniformLocation(this.program, 'uMatrix');
    this.uCamera = gl.getUniformLocation(this.program, 'uCamera');
    this.aPos = gl.getAttribLocation(this.program, 'aPos');
  }
}
```

**设计特点：**
- **着色器管理**：完整的着色器编译和链接流程
- **错误处理**：编译失败时抛出详细错误信息
- **资源管理**：统一管理 WebGL 资源的生命周期

### 3. LineRenderer 线条渲染器

#### 顶点着色器分析
```glsl
precision highp float;

uniform mat4 uMatrix;      // 变换矩阵
uniform float uThickness;  // 线条厚度
uniform vec4 uCamera;      // 相机位置和参数
uniform vec2 uOffset;      // 偏移向量
attribute vec4 aPos;       // 顶点位置 (x, y, z, item)
attribute float aDist;     // 距离信息

void main() {
  vec3 pos = aPos.xyz;
  float item = aPos.w;
  float side = -1.0;
  
  // 解析顶点类型
  if(item < 3.0) {
    item -= 2.0;
  } else {
    side = 1.0;
    item -= 3.0;
  }
  
  // 高度偏移和相机补偿
  pos += alt;
  pos += normalize(uCamera.xyz - pos) * 0.000001;
  
  // 投影变换
  vec4 xyPos = uMatrix * vec4(pos, 1.0);
  
  // 线条厚度处理
  xyPos += vec4(uOffset, 0.0, 0.0) * side * xyPos.w * uThickness;
  
  // 距离衰减计算
  vAlpha = 1.0 - smoothstep(max(5.0, uCamera.w), max(7.5, uCamera.w * 2.0), 
                           length(uCamera.xyz - pos) / uCamera.z);
  
  gl_Position = xyPos;
}
```

**着色器技术要点：**
- **线条厚度实现**：通过偏移顶点位置实现可变厚度
- **距离衰减**：远距离线条自动变透明
- **相机补偿**：防止 Z-fighting 问题
- **高精度计算**：使用 `highp` 精度确保准确性

#### 片段着色器分析
```glsl
precision highp float;

uniform float uAlpha;  // 全局透明度
uniform float uDist;   // 当前进度距离
varying float vItem;   // 颜色类型
varying float vAlpha;  // 距离衰减透明度
varying float vDist;   // 顶点距离

void main() {
  // 进度裁剪
  if(vDist > uDist) discard;
  
  // 颜色计算
  if(vItem == 1.0) {
    gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0) * uAlpha * vAlpha;  // 白色
  } else {
    gl_FragColor = vec4(1.0, 1.0 - vItem, 0.0, 1.0) * uAlpha * vAlpha;  // 渐变色
  }
}
```

**颜色系统：**
- **进度控制**：只渲染当前进度之前的部分
- **数据驱动颜色**：根据 `vItem` 值计算颜色
- **透明度混合**：全局透明度和距离衰减的组合

#### 多重渲染策略
```typescript
render(gl, matrix, thickness, position, camera) {
  // 设置基础参数
  gl.useProgram(this.program);
  gl.uniformMatrix4fv(this.uMatrix, false, matrix);
  
  // 四个方向的偏移渲染
  gl.uniform2f(this.uOffset, Math.SQRT2 / 2, Math.SQRT2 / 2);
  gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);
  
  gl.uniform2f(this.uOffset, Math.SQRT2 / 2, -Math.SQRT2 / 2);
  gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);
  
  gl.uniform2f(this.uOffset, 1, 0);
  gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);
  
  gl.uniform2f(this.uOffset, 0, 1);
  gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);
  
  // 阴影效果
  gl.disable(gl.DEPTH_TEST);
  gl.uniform1f(this.uAlpha, 0.125);
  gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2);
  gl.enable(gl.DEPTH_TEST);
}
```

**渲染技术：**
- **多重绘制**：四个方向的偏移创建厚线条效果
- **阴影效果**：禁用深度测试绘制半透明阴影
- **抗锯齿**：通过多重采样实现平滑边缘
